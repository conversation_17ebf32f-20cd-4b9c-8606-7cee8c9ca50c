#!/usr/bin/env python3
"""
MACE Structure Relaxation Script for HfO2 with Symmetry Preservation

This script performs MACE structure relaxation following the VibroML methodology
while preserving the original crystal symmetry (SG67, Cmma).
"""

import os
import sys
import numpy as np
from pathlib import Path

# Add VibroML to path
vibroml_path = Path(__file__).parent
sys.path.insert(0, str(vibroml_path))

try:
    from pymatgen.core import Structure
    from pymatgen.io.ase import AseAtomsAdaptor
    from pymatgen.symmetry.analyzer import SpacegroupAnalyzer
    from ase.constraints import UnitCellFilter
    from ase.optimize import BFGS
    from ase.io import write
    import spglib
except ImportError as e:
    print(f"Error importing required packages: {e}")
    print("Please ensure pymatgen, ASE, and spglib are installed")
    sys.exit(1)

# Import VibroML utilities
try:
    from vibroml.utils.structure_utils import initialize_calculator, load_structure
    from vibroml.utils.relaxation_utils import analyze_symmetry
    from vibroml.utils.utils import HAVE_MACE
except ImportError as e:
    print(f"Error importing VibroML utilities: {e}")
    print("Please ensure VibroML is properly installed")
    sys.exit(1)


def analyze_initial_structure(cif_path):
    """Analyze the initial structure and verify its symmetry."""
    print("=" * 60)
    print("INITIAL STRUCTURE ANALYSIS")
    print("=" * 60)
    
    # Load structure using VibroML method
    struct, atoms = load_structure(cif_path)
    if struct is None or atoms is None:
        print("Failed to load structure")
        return None, None
    
    print(f"Structure loaded: {len(atoms)} atoms")
    print(f"Chemical formula: {struct.composition.reduced_formula}")
    
    # Analyze with pymatgen SpacegroupAnalyzer
    sga = SpacegroupAnalyzer(struct)
    print(f"\nPymatgen Analysis:")
    print(f"  Space group symbol: {sga.get_space_group_symbol()}")
    print(f"  Space group number: {sga.get_space_group_number()}")
    print(f"  Crystal system: {sga.get_crystal_system()}")
    print(f"  Point group: {sga.get_point_group_symbol()}")
    
    # Cell parameters
    a, b, c = struct.lattice.abc
    alpha, beta, gamma = struct.lattice.angles
    print(f"\nInitial Cell Parameters:")
    print(f"  a = {a:.6f} Å")
    print(f"  b = {b:.6f} Å") 
    print(f"  c = {c:.6f} Å")
    print(f"  α = {alpha:.2f}°")
    print(f"  β = {beta:.2f}°")
    print(f"  γ = {gamma:.2f}°")
    print(f"  Volume = {struct.lattice.volume:.2f} Å³")
    
    # Verify expected space group
    expected_sg = 67
    actual_sg = sga.get_space_group_number()
    if actual_sg == expected_sg:
        print(f"✓ Confirmed space group {expected_sg} (Cmma)")
    else:
        print(f"⚠ WARNING: Expected SG{expected_sg}, found SG{actual_sg}")
    
    return struct, atoms


def setup_mace_calculator():
    """Initialize MACE calculator using VibroML settings."""
    print("\n" + "=" * 60)
    print("MACE CALCULATOR SETUP")
    print("=" * 60)
    
    if not HAVE_MACE:
        print("ERROR: MACE not available. Please install with: pip install mace-torch")
        return None
    
    # Use VibroML default settings
    engine = "mace"
    model_name = "medium-omat-0"  # From default_settings.json
    
    calculator = initialize_calculator(engine, model_name)
    if calculator is None:
        print("Failed to initialize MACE calculator")
        return None
    
    print("✓ MACE calculator initialized successfully")
    return calculator


def perform_mace_relaxation(atoms, calculator, output_dir, fmax=0.001):
    """Perform MACE relaxation with symmetry monitoring."""
    print("\n" + "=" * 60)
    print("MACE STRUCTURE RELAXATION")
    print("=" * 60)
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Initial symmetry analysis using VibroML method
    print("\n--- Initial Symmetry Analysis ---")
    analyze_symmetry(atoms, output_dir, prefix="initial", auto_tune_symprec=True)
    
    # Set up relaxation
    atoms.set_calculator(calculator)
    initial_atoms = atoms.copy()
    
    # Get initial energy and forces
    try:
        initial_energy = atoms.get_potential_energy()
        initial_forces = atoms.get_forces()
        max_initial_force = np.sqrt((initial_forces**2).sum(axis=1)).max()
        print(f"\nInitial Energy: {initial_energy:.6f} eV")
        print(f"Initial Max Force: {max_initial_force:.6f} eV/Å")
    except Exception as e:
        print(f"Warning: Could not get initial energy/forces: {e}")
        initial_energy = None
        max_initial_force = None
    
    # Set up optimizer with UnitCellFilter (allows cell relaxation)
    ucf = UnitCellFilter(atoms)
    
    # Use BFGS optimizer as in VibroML
    relax_log_path = os.path.join(output_dir, "mace_relaxation.log")
    relax_traj_path = os.path.join(output_dir, "mace_relaxation.traj")
    
    opt = BFGS(ucf, logfile=relax_log_path, trajectory=relax_traj_path)
    
    print(f"\nStarting MACE relaxation...")
    print(f"  Force tolerance: {fmax} eV/Å")
    print(f"  Log file: {relax_log_path}")
    print(f"  Trajectory: {relax_traj_path}")
    
    try:
        opt.run(fmax=fmax)
        relaxed_atoms = ucf.atoms.copy()
        print("✓ Relaxation completed successfully")
    except Exception as e:
        print(f"ERROR during relaxation: {e}")
        return None, None
    
    # Get final energy and forces
    try:
        final_energy = relaxed_atoms.get_potential_energy()
        final_forces = relaxed_atoms.get_forces()
        max_final_force = np.sqrt((final_forces**2).sum(axis=1)).max()
        
        print(f"\nFinal Energy: {final_energy:.6f} eV")
        print(f"Final Max Force: {max_final_force:.6f} eV/Å")
        
        if initial_energy is not None:
            energy_change = final_energy - initial_energy
            print(f"Energy Change: {energy_change:.6f} eV ({energy_change/len(atoms):.6f} eV/atom)")
        
        # Check convergence
        if max_final_force <= fmax:
            print(f"✓ Relaxation converged (max force {max_final_force:.6f} <= {fmax})")
        else:
            print(f"⚠ WARNING: Not fully converged (max force {max_final_force:.6f} > {fmax})")
            
    except Exception as e:
        print(f"Warning: Could not get final energy/forces: {e}")
        final_energy = None
        max_final_force = None
    
    return initial_atoms, relaxed_atoms


def analyze_relaxed_structure(initial_atoms, relaxed_atoms, output_dir):
    """Analyze the relaxed structure and compare with initial."""
    print("\n" + "=" * 60)
    print("RELAXED STRUCTURE ANALYSIS")
    print("=" * 60)
    
    # Final symmetry analysis using VibroML method
    print("\n--- Final Symmetry Analysis ---")
    analyze_symmetry(relaxed_atoms, output_dir, prefix="relaxed", auto_tune_symprec=True)
    
    # Convert to pymatgen for detailed analysis
    initial_struct = AseAtomsAdaptor().get_structure(initial_atoms)
    relaxed_struct = AseAtomsAdaptor().get_structure(relaxed_atoms)
    
    # Symmetry comparison
    initial_sga = SpacegroupAnalyzer(initial_struct)
    relaxed_sga = SpacegroupAnalyzer(relaxed_struct)
    
    initial_sg = initial_sga.get_space_group_number()
    relaxed_sg = relaxed_sga.get_space_group_number()
    
    print(f"\n--- Symmetry Comparison ---")
    print(f"Initial space group: {initial_sga.get_space_group_symbol()} (SG{initial_sg})")
    print(f"Relaxed space group: {relaxed_sga.get_space_group_symbol()} (SG{relaxed_sg})")
    
    if initial_sg == relaxed_sg:
        print("✓ Space group preserved during relaxation")
    else:
        print("⚠ WARNING: Space group changed during relaxation")
    
    # Cell parameter comparison
    print(f"\n--- Cell Parameter Changes ---")
    initial_abc = initial_struct.lattice.abc
    relaxed_abc = relaxed_struct.lattice.abc
    initial_angles = initial_struct.lattice.angles
    relaxed_angles = relaxed_struct.lattice.angles
    
    for i, param in enumerate(['a', 'b', 'c']):
        initial_val = initial_abc[i]
        relaxed_val = relaxed_abc[i]
        change = relaxed_val - initial_val
        percent_change = (change / initial_val) * 100
        print(f"  {param}: {initial_val:.6f} → {relaxed_val:.6f} Å "
              f"(Δ{change:+.6f}, {percent_change:+.3f}%)")
    
    for i, angle in enumerate(['α', 'β', 'γ']):
        initial_val = initial_angles[i]
        relaxed_val = relaxed_angles[i]
        change = relaxed_val - initial_val
        print(f"  {angle}: {initial_val:.2f} → {relaxed_val:.2f}° (Δ{change:+.2f}°)")
    
    # Volume change
    initial_vol = initial_struct.lattice.volume
    relaxed_vol = relaxed_struct.lattice.volume
    vol_change = relaxed_vol - initial_vol
    vol_percent_change = (vol_change / initial_vol) * 100
    print(f"  Volume: {initial_vol:.2f} → {relaxed_vol:.2f} Å³ "
          f"(Δ{vol_change:+.2f}, {vol_percent_change:+.3f}%)")
    
    return relaxed_struct


def save_relaxed_structure(relaxed_struct, output_path):
    """Save the relaxed structure to CIF format."""
    print(f"\n--- Saving Relaxed Structure ---")
    relaxed_struct.to(filename=output_path)
    print(f"✓ Relaxed structure saved to: {output_path}")


def main():
    """Main function to perform MACE relaxation of HfO2."""
    print("MACE Structure Relaxation for HfO2 (SG67, Cmma)")
    print("Following VibroML Methodology")
    print("=" * 60)
    
    # Input and output paths
    cif_path = "examples/HfO2/HfO2_SG67_Cmma_ini.cif"
    output_dir = "examples/HfO2/mace_relaxation_output"
    relaxed_cif_path = "examples/HfO2/HfO2_SG67_Cmma_relaxed.cif"
    
    # Check if input file exists
    if not os.path.exists(cif_path):
        print(f"ERROR: Input file not found: {cif_path}")
        sys.exit(1)
    
    # Step 1: Analyze initial structure
    initial_struct, initial_atoms = analyze_initial_structure(cif_path)
    if initial_struct is None:
        sys.exit(1)
    
    # Step 2: Setup MACE calculator
    calculator = setup_mace_calculator()
    if calculator is None:
        sys.exit(1)
    
    # Step 3: Perform relaxation
    fmax = 0.001  # From VibroML default settings
    initial_atoms_copy, relaxed_atoms = perform_mace_relaxation(
        initial_atoms.copy(), calculator, output_dir, fmax
    )
    
    if relaxed_atoms is None:
        print("ERROR: Relaxation failed")
        sys.exit(1)
    
    # Step 4: Analyze results
    relaxed_struct = analyze_relaxed_structure(initial_atoms_copy, relaxed_atoms, output_dir)
    
    # Step 5: Save relaxed structure
    save_relaxed_structure(relaxed_struct, relaxed_cif_path)
    
    print("\n" + "=" * 60)
    print("MACE RELAXATION COMPLETED SUCCESSFULLY")
    print("=" * 60)
    print(f"Input:  {cif_path}")
    print(f"Output: {relaxed_cif_path}")
    print(f"Analysis files: {output_dir}/")


if __name__ == "__main__":
    main()
